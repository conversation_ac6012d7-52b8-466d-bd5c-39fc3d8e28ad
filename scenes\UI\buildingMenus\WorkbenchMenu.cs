using Godot;

public partial class WorkbenchMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _pickaxeUpgradeButton;
	private Button _hammerUpgradeButton;
	private Button _hoeUpgradeButton;
	private Button _swordUpgradeButton;
	private Button _bowUpgradeButton;
	private Button _wateringCanUpgradeButton;
	private Sprite2D _pickaxeBuildButtonSprite;
	private Sprite2D _hammerBuildButtonSprite;
	private Sprite2D _hoeBuildButtonSprite;
	private Sprite2D _swordBuildButtonSprite;
	private Sprite2D _bowBuildButtonSprite;
	private Sprite2D _wateringCanBuildButtonSprite;
	private Workbench _workbench;



	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		if (_animationPlayer == null)
		{
			GD.PrintErr("AnvilMenu: AnimationPlayer not found!");
			return;
		}

		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		if (_closeButton == null)
		{
			GD.PrintErr("AnvilMenu: Close button not found!");
			return;
		}

		_pickaxeUpgradeButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe/Button");
		if (_pickaxeUpgradeButton == null)
		{
			GD.PrintErr("WorkbenchMenu: Pickaxe upgrade button not found!");
			return;
		}

		_hammerUpgradeButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer/Button");
		if (_hammerUpgradeButton == null)
		{
			GD.PrintErr("WorkbenchMenu: Hammer upgrade button not found!");
			return;
		}

		_hoeUpgradeButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe/Button");
		if (_hoeUpgradeButton == null)
		{
			GD.PrintErr("WorkbenchMenu: Hoe upgrade button not found!");
			return;
		}

		_swordUpgradeButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListSword/Button");
		if (_swordUpgradeButton == null)
		{
			GD.PrintErr("WorkbenchMenu: Sword upgrade button not found!");
			return;
		}

		_bowUpgradeButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListBow/Button");
		if (_bowUpgradeButton == null)
		{
			GD.PrintErr("WorkbenchMenu: Bow upgrade button not found!");
			return;
		}

		_wateringCanUpgradeButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan/Button");
		if (_wateringCanUpgradeButton == null)
		{
			GD.PrintErr("WorkbenchMenu: Watering Can upgrade button not found!");
			return;
		}

		_pickaxeBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe/BuildButton");
		_hammerBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer/BuildButton");
		_hoeBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe/BuildButton");
		_swordBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListSword/BuildButton");
		_bowBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListBow/BuildButton");
		_wateringCanBuildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan/BuildButton");

		_closeButton.Pressed += OnCloseButtonPressed;
		_pickaxeUpgradeButton.Pressed += OnPickaxeUpgradeButtonPressed;
		_hammerUpgradeButton.Pressed += OnHammerUpgradeButtonPressed;
		_hoeUpgradeButton.Pressed += OnHoeUpgradeButtonPressed;
		_swordUpgradeButton.Pressed += OnSwordUpgradeButtonPressed;
		_bowUpgradeButton.Pressed += OnBowUpgradeButtonPressed;
		_wateringCanUpgradeButton.Pressed += OnWateringCanUpgradeButtonPressed;

		// Set Control/Panel to initially hidden
		var panel = GetNode<Sprite2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		// Register with MenuManager
		MenuManager.Instance?.RegisterMenu("WorkbenchMenu", this);
	}

	public void SetWorkbench(Workbench workbench)
	{
		_workbench = workbench;
	}

	public void OpenMenu()
	{
		UpdateAllUpgradeButtonStates();

		// Disable player movement when menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Open");
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	public void CloseMenu()
	{
		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Close");
		}
	}

	public bool IsMenuOpen()
	{
		return GetNode<Sprite2D>("Control/Panel").Visible;
	}

	private void OnPickaxeUpgradeButtonPressed()
	{
		if (!CanAffordPickaxeUpgrade())
		{
			GD.Print("WorkbenchMenu: Not enough resources to upgrade pickaxe!");
			return;
		}

		CloseMenu();

		if (_workbench != null)
		{
			_workbench.StartUpgradingTool(ToolType.Pickaxe);
		}
		else
		{
			GD.PrintErr("WorkbenchMenu: Workbench reference not set!");
		}
	}

	private void OnHammerUpgradeButtonPressed()
	{
		if (!CanAffordHammerUpgrade())
		{
			GD.Print("WorkbenchMenu: Not enough resources to upgrade hammer!");
			return;
		}

		CloseMenu();

		if (_workbench != null)
		{
			_workbench.StartUpgradingTool(ToolType.Hammer);
		}
		else
		{
			GD.PrintErr("WorkbenchMenu: Workbench reference not set!");
		}
	}

	private void OnHoeUpgradeButtonPressed()
	{
		if (!CanAffordHoeUpgrade())
		{
			GD.Print("WorkbenchMenu: Not enough resources to upgrade hoe!");
			return;
		}

		CloseMenu();

		if (_workbench != null)
		{
			_workbench.StartUpgradingTool(ToolType.Hoe);
		}
		else
		{
			GD.PrintErr("WorkbenchMenu: Workbench reference not set!");
		}
	}

	private void OnSwordUpgradeButtonPressed()
	{
		if (!CanAffordSwordUpgrade())
		{
			GD.Print("WorkbenchMenu: Not enough resources to upgrade sword!");
			return;
		}

		CloseMenu();

		if (_workbench != null)
		{
			_workbench.StartUpgradingTool(ToolType.Sword);
		}
		else
		{
			GD.PrintErr("WorkbenchMenu: Workbench reference not set!");
		}
	}

	private void OnBowUpgradeButtonPressed()
	{
		if (!CanAffordBowUpgrade())
		{
			GD.Print("WorkbenchMenu: Not enough resources to upgrade bow!");
			return;
		}

		CloseMenu();

		if (_workbench != null)
		{
			_workbench.StartUpgradingTool(ToolType.Bow);
		}
		else
		{
			GD.PrintErr("WorkbenchMenu: Workbench reference not set!");
		}
	}

	private void OnWateringCanUpgradeButtonPressed()
	{
		if (!CanAffordWateringCanUpgrade())
		{
			GD.Print("WorkbenchMenu: Not enough resources to upgrade watering can!");
			return;
		}

		CloseMenu();

		if (_workbench != null)
		{
			_workbench.StartUpgradingTool(ToolType.WateringCan);
		}
		else
		{
			GD.PrintErr("WorkbenchMenu: Workbench reference not set!");
		}
	}

	private bool CanAffordToolUpgrade(ToolType toolType)
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		int currentLevel = resourcesManager.GetToolLevel(toolType);
		int nextLevel = currentLevel + 1;

		if (nextLevel > 10) return false; // Max level reached

		if (nextLevel == 2) // Stone level
		{
			return resourcesManager.HasResource(ResourceType.Stone, 20) &&
				   resourcesManager.HasResource(ResourceType.Stone2, 20);
		}
		else if (nextLevel == 3) // Copper
		{
			return resourcesManager.HasResource(ResourceType.WoodenBeam, 10) &&
				   resourcesManager.HasResource(ResourceType.CopperBar, 20);
		}
		else if (nextLevel == 4) // Iron
		{
			return resourcesManager.HasResource(ResourceType.WoodenBeam, 10) &&
				   resourcesManager.HasResource(ResourceType.IronBar, 20);
		}
		else if (nextLevel == 5) // Gold
		{
			return resourcesManager.HasResource(ResourceType.WoodenBeam, 10) &&
				   resourcesManager.HasResource(ResourceType.GoldBar, 20);
		}
		else if (nextLevel == 6) // Indigosium
		{
			return resourcesManager.HasResource(ResourceType.WoodenBeam, 10) &&
				   resourcesManager.HasResource(ResourceType.IndigosiumBar, 20);
		}
		else if (nextLevel == 7) // Mithril
		{
			return resourcesManager.HasResource(ResourceType.WoodenBeam, 10) &&
				   resourcesManager.HasResource(ResourceType.MithrilBar, 20);
		}
		else if (nextLevel == 8) // Erithrydium
		{
			return resourcesManager.HasResource(ResourceType.WoodenBeam, 10) &&
				   resourcesManager.HasResource(ResourceType.ErithrydiumBar, 20);
		}
		else if (nextLevel == 9) // Adamantite
		{
			return resourcesManager.HasResource(ResourceType.WoodenBeam, 10) &&
				   resourcesManager.HasResource(ResourceType.AdamantiteBar, 20);
		}
		else if (nextLevel == 10) // Uranium
		{
			return resourcesManager.HasResource(ResourceType.WoodenBeam, 10) &&
				   resourcesManager.HasResource(ResourceType.UraniumBar, 20);
		}

		return false;
	}

	private bool CanAffordPickaxeUpgrade()
	{
		return CanAffordToolUpgrade(ToolType.Pickaxe);
	}

	private bool CanAffordHammerUpgrade()
	{
		return CanAffordToolUpgrade(ToolType.Hammer);
	}

	private bool CanAffordHoeUpgrade()
	{
		return CanAffordToolUpgrade(ToolType.Hoe);
	}

	private bool CanAffordSwordUpgrade()
	{
		return CanAffordToolUpgrade(ToolType.Sword);
	}

	private bool CanAffordBowUpgrade()
	{
		return CanAffordToolUpgrade(ToolType.Bow);
	}

	private bool CanAffordWateringCanUpgrade()
	{
		return CanAffordToolUpgrade(ToolType.WateringCan);
	}

	private void UpdateAllUpgradeButtonStates()
	{
		UpdateUpgradeButtonState(_pickaxeBuildButtonSprite, _pickaxeUpgradeButton, CanAffordPickaxeUpgrade());
		UpdateUpgradeButtonState(_hammerBuildButtonSprite, _hammerUpgradeButton, CanAffordHammerUpgrade());
		UpdateUpgradeButtonState(_hoeBuildButtonSprite, _hoeUpgradeButton, CanAffordHoeUpgrade());
		UpdateUpgradeButtonState(_swordBuildButtonSprite, _swordUpgradeButton, CanAffordSwordUpgrade());
		UpdateUpgradeButtonState(_bowBuildButtonSprite, _bowUpgradeButton, CanAffordBowUpgrade());
		UpdateUpgradeButtonState(_wateringCanBuildButtonSprite, _wateringCanUpgradeButton, CanAffordWateringCanUpgrade());
	}

	private void UpdateUpgradeButtonState(Sprite2D buildButtonSprite, Button upgradeButton, bool canAfford)
	{
		if (buildButtonSprite != null)
		{
			if (canAfford)
			{
				buildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				buildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}

		if (upgradeButton != null)
		{
			upgradeButton.Disabled = !canAfford;
		}
	}



	public override void _ExitTree()
	{
		if (_closeButton != null)
		{
			_closeButton.Pressed -= OnCloseButtonPressed;
		}
		if (_pickaxeUpgradeButton != null)
		{
			_pickaxeUpgradeButton.Pressed -= OnPickaxeUpgradeButtonPressed;
		}
		if (_hammerUpgradeButton != null)
		{
			_hammerUpgradeButton.Pressed -= OnHammerUpgradeButtonPressed;
		}
		if (_hoeUpgradeButton != null)
		{
			_hoeUpgradeButton.Pressed -= OnHoeUpgradeButtonPressed;
		}
		if (_swordUpgradeButton != null)
		{
			_swordUpgradeButton.Pressed -= OnSwordUpgradeButtonPressed;
		}
		if (_bowUpgradeButton != null)
		{
			_bowUpgradeButton.Pressed -= OnBowUpgradeButtonPressed;
		}
		if (_wateringCanUpgradeButton != null)
		{
			_wateringCanUpgradeButton.Pressed -= OnWateringCanUpgradeButtonPressed;
		}
	}
}
