[gd_scene load_steps=8 format=3 uid="uid://workbench123"]

[ext_resource type="Script" path="res://scenes/mapObjects/buildings/Workbench.cs" id="1_workbench"]
[ext_resource type="Texture2D" uid="uid://8dd4tcykr04k" path="res://resources/solaria/buildings/anvil_foreground.png" id="3_workbench_sprite"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_progress_bar"]
[ext_resource type="PackedScene" path="res://scenes/UI/buildingMenus/WorkbenchMenu.tscn" id="4_workbench_menu"]
[ext_resource type="PackedScene" uid="uid://b8xf7h2lam3pq" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="5_upgrading_progress"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_workbench"]
size = Vector2(15, 9)

[sub_resource type="CircleShape2D" id="CircleShape2D_workbench_detector"]

[node name="Workbench" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_workbench")

[node name="WorkbenchSprite" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("3_workbench_sprite")

[node name="UpgradingTool" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(1, 18)
scale = Vector2(0.75, 0.75)
offset = Vector2(0, -30)

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0.5, 3.5)
shape = SubResource("RectangleShape2D_workbench")

[node name="ProgressBar" parent="." instance=ExtResource("3_progress_bar")]
position = Vector2(0.4, 9)
scale = Vector2(1.05, 0.6)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(1, 1)
shape = SubResource("CircleShape2D_workbench_detector")

[node name="WorkbenchMenu" parent="." instance=ExtResource("4_workbench_menu")]

[node name="ProgressBarVertical" parent="." instance=ExtResource("5_upgrading_progress")]
visible = false
z_index = 1
position = Vector2(16, -1)
