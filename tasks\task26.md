1. We want to add workbench. Duplicate anvil (tscn, cs) and anvil menu (tscn, cs) - call it Workbench. We will use it to upgradte tools and wapons. When player clicks build it should start upgrade. We want to have: bow, sword, hoe, pickaxe, hammer, watering can to be upgraded. And starting from level 1 there are variants: wood (default, owned), stone, copper, iron, gold, indigosium, mithril, erithrydium, adamantite, uranium. Stone one needs 20 stone and 20 stone2 (sand stone). Each next needs 10 wooden beam and 20 bars of given metal (so to upgrade to copper you need 10 wooden beams and 20 copper bars etc). When time required for updating given tool passes then this tool is updated. it doesnt have to be picked up. When it's update is finnished - update this item level. For menu - when you duplicate anvil - you can use item list like the one defined in anvil - you can just duplicate it and adjust text, values.
Workbench will be 2x1 tile size (32x16) - so just like an anvil. 
I want to be able to place it on map.
I will set workbench sprite.
When player hit it by axe - it should be damaged with animation like anvil.
When player hit by hammer - fix just like anvil.
You have 
2. <PERSON><PERSON><PERSON><PERSON> does not close menu when i click ESC - fix.