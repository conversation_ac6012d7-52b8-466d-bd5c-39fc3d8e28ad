[gd_scene load_steps=19 format=3 uid="uid://dcajtv8sdwps"]

[ext_resource type="Script" uid="uid://4i7cw5efctxo" path="res://scenes/UI/buildingMenus/WorkbenchMenu.cs" id="1_workbench_menu"]
[ext_resource type="Texture2D" uid="uid://cyvhsyv6xia6m" path="res://resources/solaria/UI/build/build_panel.png" id="2_panel"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="3_slot"]
[ext_resource type="Texture2D" uid="uid://qtnmf3qqfvr5" path="res://resources/solaria/UI/build/buildButton.png" id="4_button"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="5_close"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="6_label"]

[sub_resource type="Animation" id="Animation_close"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_open"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_workbench"]
_data = {
&"Close": SubResource("Animation_close"),
&"Open": SubResource("Animation_open")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0g21i"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_oi76f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ayp1v"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0o87f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ehmlw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_x30ib"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rmikb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_uss5x"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jvsc2"]

[node name="WorkbenchMenu" type="CanvasLayer"]
script = ExtResource("1_workbench_menu")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_workbench")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
visible = false
scale = Vector2(0.95, 0.95)
texture = ExtResource("2_panel")

[node name="CloseButton" type="Button" parent="Control/Panel"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -114.0
offset_top = -140.0
offset_right = -74.0
offset_bottom = -100.0
grow_horizontal = 0
icon = ExtResource("5_close")
flat = true

[node name="ScrollContainer" type="ScrollContainer" parent="Control/Panel"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -92.0
offset_top = -116.0
offset_right = -99.0
offset_bottom = -124.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Control/Panel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ItemListPickaxe" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_0g21i")
theme_override_styles/panel = SubResource("StyleBoxEmpty_oi76f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_ayp1v")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_0o87f")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_ehmlw")
theme_override_styles/selected = SubResource("StyleBoxEmpty_x30ib")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_rmikb")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_uss5x")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_jvsc2")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 150.0
offset_top = 4.0
offset_bottom = -5.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 142.06
offset_bottom = 18.0
scale = Vector2(0.835, 0.835)
text = "Pickaxe"
horizontal_alignment = 0

[node name="ItemListHammer" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_0g21i")
theme_override_styles/panel = SubResource("StyleBoxEmpty_oi76f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_ayp1v")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_0o87f")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_ehmlw")
theme_override_styles/selected = SubResource("StyleBoxEmpty_x30ib")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_rmikb")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_uss5x")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_jvsc2")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 142.06
offset_bottom = 18.0
scale = Vector2(0.835, 0.835)
text = "Hammer"
horizontal_alignment = 0

[node name="ItemListHoe" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_0g21i")
theme_override_styles/panel = SubResource("StyleBoxEmpty_oi76f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_ayp1v")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_0o87f")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_ehmlw")
theme_override_styles/selected = SubResource("StyleBoxEmpty_x30ib")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_rmikb")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_uss5x")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_jvsc2")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 150.0
offset_top = 4.0
offset_bottom = -5.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 142.06
offset_bottom = 18.0
scale = Vector2(0.835, 0.835)
text = "Hoe"
horizontal_alignment = 0

[node name="ItemListSword" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_0g21i")
theme_override_styles/panel = SubResource("StyleBoxEmpty_oi76f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_ayp1v")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_0o87f")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_ehmlw")
theme_override_styles/selected = SubResource("StyleBoxEmpty_x30ib")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_rmikb")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_uss5x")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_jvsc2")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.847458
anchor_top = 0.075
anchor_right = 1.0113
anchor_bottom = 0.9
grow_horizontal = 2
grow_vertical = 2
flat = true
metadata/_edit_use_anchors_ = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 142.06
offset_bottom = 18.0
scale = Vector2(0.835, 0.835)
text = "Sword"
horizontal_alignment = 0

[node name="ItemListBow" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_0g21i")
theme_override_styles/panel = SubResource("StyleBoxEmpty_oi76f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_ayp1v")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_0o87f")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_ehmlw")
theme_override_styles/selected = SubResource("StyleBoxEmpty_x30ib")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_rmikb")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_uss5x")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_jvsc2")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 150.0
offset_top = 3.0
offset_right = 3.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 142.06
offset_bottom = 18.0
scale = Vector2(0.835, 0.835)
text = "Bow"
horizontal_alignment = 0

[node name="ItemListWateringCan" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_0g21i")
theme_override_styles/panel = SubResource("StyleBoxEmpty_oi76f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_ayp1v")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_0o87f")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_ehmlw")
theme_override_styles/selected = SubResource("StyleBoxEmpty_x30ib")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_rmikb")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_uss5x")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_jvsc2")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 150.0
offset_top = 4.0
offset_right = 1.0
offset_bottom = -13.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 142.06
offset_bottom = 18.0
scale = Vector2(0.835, 0.835)
text = "Watering Can"
horizontal_alignment = 0
