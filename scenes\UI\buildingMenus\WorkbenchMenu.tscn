[gd_scene load_steps=10 format=3 uid="uid://workbench_menu_tscn"]

[ext_resource type="Script" path="res://scenes/UI/buildingMenus/WorkbenchMenu.cs" id="1_workbench_menu"]
[ext_resource type="Texture2D" uid="uid://cyvhsyv6xia6m" path="res://resources/solaria/UI/build/build_panel.png" id="2_panel"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="3_slot"]
[ext_resource type="Texture2D" uid="uid://qtnmf3qqfvr5" path="res://resources/solaria/UI/build/buildButton.png" id="4_button"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="5_close"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="6_label"]

[sub_resource type="Animation" id="Animation_close"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_open"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_workbench"]
_data = {
&"Close": SubResource("Animation_close"),
&"Open": SubResource("Animation_open")
}

[node name="WorkbenchMenu" type="CanvasLayer"]
script = ExtResource("1_workbench_menu")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_workbench")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
visible = false
scale = Vector2(0.95, 0.95)
texture = ExtResource("2_panel")

[node name="CloseButton" type="Button" parent="Control/Panel"]
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = 80.0
offset_top = -120.0
offset_right = 96.0
offset_bottom = -104.0
grow_horizontal = 0
icon = ExtResource("5_close")
flat = true

[node name="ScrollContainer" type="ScrollContainer" parent="Control/Panel"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -92.0
offset_top = -116.0
offset_right = -99.0
offset_bottom = -124.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Control/Panel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ItemListPickaxe" type="Control" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 118.0
offset_bottom = 18.0
text = "Pickaxe"

[node name="ItemListHammer" type="Control" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 118.0
offset_bottom = 18.0
text = "Hammer"

[node name="ItemListHoe" type="Control" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 118.0
offset_bottom = 18.0
text = "Hoe"

[node name="ItemListSword" type="Control" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 118.0
offset_bottom = 18.0
text = "Sword"

[node name="ItemListBow" type="Control" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 118.0
offset_bottom = 18.0
text = "Bow"

[node name="ItemListWateringCan" type="Control" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(17, 20)
texture = ExtResource("3_slot")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(165, 21)
scale = Vector2(1.49, 1.49)
texture = ExtResource("4_button")

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
flat = true

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan" instance=ExtResource("6_label")]
layout_mode = 0
offset_left = 34.0
offset_top = 1.0
offset_right = 118.0
offset_bottom = 18.0
text = "Watering Can"
