using Godot;
using System;
using System.Collections.Generic;

public partial class Furnace1 : Node2D, IDestroyableObject
{
	[Export] public ObjectType BuildingType { get; set; } = ObjectType.Furnace1;
	[Export] public int MaxHealth { get; set; } = 25;

	private const int BUILDING_WIDTH = 2;
	private const int BUILDING_HEIGHT = 2;
	private const int TILE_SIZE = 16;
	private const int MAX_HEALTH = 25;

	private int _currentHealth;
	private Vector2I _topLeftTilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isPlaced = false;
	private bool _isBeingDestroyed = false;
	private string _saveId = "";

	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	private Sprite2D _craftingResourceSprite;
	private ProgressBar _healthBar;
	private ProgressBarVertical _craftingProgressBar;
	private Area2D _interactionArea;

	private ResourceType _selectedCraftingResource = ResourceType.None;
	private int _amountToProduce = 1;
	private Timer _smeltingTimer;
	private bool _isSmelting = false;
	private int _currentSmeltingProgress = 0;

	private readonly Dictionary<ResourceType, int> SmeltingTimeSeconds = new()
	{
		{ ResourceType.CopperBar, 15 },
		{ ResourceType.IronBar, 20 },
		{ ResourceType.CopperKey, 10 },
		{ ResourceType.IronKey, 12 }
	};

	private readonly Dictionary<ResourceType, Dictionary<ResourceType, int>> SmeltingRecipes = new()
	{
		{
			ResourceType.CopperBar,
			new Dictionary<ResourceType, int>
			{
				{ ResourceType.CopperOre, 3 },
				{ ResourceType.Wood, 1 }
			}
		},
		{
			ResourceType.IronBar,
			new Dictionary<ResourceType, int>
			{
				{ ResourceType.IronOre, 3 },
				{ ResourceType.Wood, 1 }
			}
		},
		{
			ResourceType.CopperKey,
			new Dictionary<ResourceType, int>
			{
				{ ResourceType.CopperBar, 4 },
				{ ResourceType.Charcoal, 1 }
			}
		},
		{
			ResourceType.IronKey,
			new Dictionary<ResourceType, int>
			{
				{ ResourceType.IronBar, 4 },
				{ ResourceType.Charcoal, 1 }
			}
		}
	};

	private bool _isMenuOpen = false;
	private bool _isPlayerInRange = false;
	private Furnace1Menu _furnaceMenu;

	private Tween _hitTween;
	private Color _normalColor = Colors.White;
	private Color _hitColor = Color.FromHtml("ff6c44");
	private float _hitTintStrength = 0.8f;
	private float _hitAnimationDuration = 0.3f;

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_sprite = GetNode<Sprite2D>("Furnace");
		_craftingResourceSprite = GetNode<Sprite2D>("CraftingResource");
		_healthBar = GetNode<ProgressBar>("ProgressBar");
		_craftingProgressBar = GetNode<ProgressBarVertical>("ProgressBarVertical");
		_interactionArea = GetNode<Area2D>("PlayerDetector");

		if (_healthBar != null)
		{
			_healthBar.Hide();
		}

		if (_craftingProgressBar != null)
		{
			_craftingProgressBar.Hide();
		}

		_smeltingTimer = new Timer();
		AddChild(_smeltingTimer);
		_smeltingTimer.WaitTime = 1.0f;
		_smeltingTimer.Timeout += OnSmeltingTimerTimeout;

		UpdateHPBar();
		UpdateCraftingResourceDisplay();

		if (_interactionArea != null)
		{
			_interactionArea.BodyEntered += OnBodyEntered;
			_interactionArea.BodyExited += OnBodyExited;
		}

		_furnaceMenu = GetNode<Furnace1Menu>("Furnace1Menu");
		if (_furnaceMenu == null)
		{
			GD.PrintErr("Furnace1: Could not find Furnace1Menu as child node");
		}
		else
		{
			GD.Print("Furnace1: Found Furnace1Menu successfully");
		}

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("Furnace1: CustomDataLayerManager not found!");
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	public override void _ExitTree()
	{
		if (_interactionArea != null)
		{
			_interactionArea.BodyEntered -= OnBodyEntered;
			_interactionArea.BodyExited -= OnBodyExited;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}



	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || !_isPlaced) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				ToggleMenu();
			}
		}
	}

	private void OnBodyEntered(Node2D body)
	{
		if (body is PlayerController && _isPlaced)
		{
			_isPlayerInRange = true;
			GD.Print("Furnace1: Player entered interaction area");
		}
	}

	private void OnBodyExited(Node2D body)
	{
		if (body is PlayerController && _isPlaced)
		{
			_isPlayerInRange = false;
			GD.Print("Furnace1: Player exited interaction area");
			if (_isMenuOpen)
			{
				CloseMenu();
			}
		}
	}

	private void ToggleMenu()
	{
		if (_isMenuOpen)
		{
			CloseMenu();
		}
		else
		{
			OpenMenu();
		}
	}

	private void OpenMenu()
	{
		if (_furnaceMenu != null && !_isMenuOpen)
		{
			_furnaceMenu.OpenMenu(this);
			_isMenuOpen = true;
			GD.Print("Furnace1: Menu opened");
		}
	}

	private void CloseMenu()
	{
		if (_furnaceMenu != null && _isMenuOpen)
		{
			_furnaceMenu.CloseMenu();
			_isMenuOpen = false;
			GD.Print("Furnace1: Menu closed");
		}
	}

	public void PlaceBuilding()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		if (!resourcesManager.HasResource(ResourceType.CopperOre, 10) ||
			!resourcesManager.HasResource(ResourceType.IronOre, 3))
		{
			GD.Print("Furnace1: Not enough resources!");
			return;
		}

		if (!resourcesManager.RemoveResource(ResourceType.CopperOre, 10) ||
			!resourcesManager.RemoveResource(ResourceType.IronOre, 3))
		{
			GD.Print("Furnace1: Failed to consume resources!");
			return;
		}

		PlaceBuilding(_topLeftTilePosition, _customDataManager);
	}

	public void PlaceBuilding(Vector2I topLeftTilePosition, CustomDataLayerManager customDataManager)
	{
		_topLeftTilePosition = topLeftTilePosition;
		_customDataManager = customDataManager;

		float centerX = (_topLeftTilePosition.X + 1.0f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 1.0f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		_isPlaced = true;

		_saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "Furnace1", (int)BuildingType);

		GD.Print($"Furnace1: Placed at {_topLeftTilePosition} (consumed 10 copper ore, 3 iron ore)");
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;

		UpdateHPBar();

		PlayHitAnimation();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("hit"))
		{
			_animationPlayer.Play("hit");
		}

		GD.Print($"Furnace1: Took {damage} damage, health: {_currentHealth}/{MaxHealth}");

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
	}

	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		var originalScale = _sprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_sprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	public void DestroyBuilding()
	{
		if (_customDataManager == null || !_isPlaced || _isBeingDestroyed) return;
		_isBeingDestroyed = true;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.ClearObjectPlaced(tilePos);
			}
		}

		if (!string.IsNullOrEmpty(_saveId))
		{
			ResourcesManager.Instance?.RemoveBuildingById(_saveId);
		}

		QueueFree();
	}

	public void SetTilePosition(Vector2I position)
	{
		_topLeftTilePosition = position;
		float centerX = (_topLeftTilePosition.X + 1.0f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 1.0f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);
	}

	public void StartSmelting(ResourceType resourceType, int amount)
	{
		if (_isSmelting) return;

		_selectedCraftingResource = resourceType;
		_amountToProduce = amount;
		_currentSmeltingProgress = 0;
		_isSmelting = true;

		UpdateCraftingResourceDisplay();
		_smeltingTimer.Start();
	}

	private void OnSmeltingTimerTimeout()
	{
		if (!_isSmelting || _selectedCraftingResource == ResourceType.None) return;

		_currentSmeltingProgress++;
		UpdateCraftingProgress();

		if (SmeltingTimeSeconds.TryGetValue(_selectedCraftingResource, out int requiredTime))
		{
			if (_currentSmeltingProgress >= requiredTime)
			{
				CompleteSmelting();
			}
		}
	}

	private void UpdateCraftingResourceDisplay()
	{
		if (_craftingResourceSprite == null) return;

		if (_selectedCraftingResource == ResourceType.None)
		{
			_craftingResourceSprite.Texture = null;
			_craftingResourceSprite.Visible = false;
		}
		else
		{
			var textureManager = TextureManager.Instance;
			if (textureManager != null)
			{
				var texture = textureManager.GetResourceTexture(_selectedCraftingResource);
				_craftingResourceSprite.Texture = texture;
				_craftingResourceSprite.Visible = true;
			}
		}
	}

	private void UpdateCraftingProgress()
	{
		if (_craftingProgressBar == null || _selectedCraftingResource == ResourceType.None) return;

		if (SmeltingTimeSeconds.TryGetValue(_selectedCraftingResource, out int requiredTime))
		{
			if (_currentSmeltingProgress > 0)
			{
				_craftingProgressBar.Show();
				float progressPercentage = (float)_currentSmeltingProgress / requiredTime;
				_craftingProgressBar.SetProgress(progressPercentage);
			}
			else
			{
				_craftingProgressBar.Hide();
			}
		}
	}

	private void CompleteSmelting()
	{
		_smeltingTimer.Stop();
		_isSmelting = false;

		SpawnSmeltedItem();

		_amountToProduce--;
		if (_amountToProduce > 0)
		{
			_currentSmeltingProgress = 0;
			_isSmelting = true;
			_smeltingTimer.Start();
		}
		else
		{
			_selectedCraftingResource = ResourceType.None;
			_currentSmeltingProgress = 0;
			UpdateCraftingResourceDisplay();
			_craftingProgressBar?.Hide();
		}
	}

	private void SpawnSmeltedItem()
	{
		Vector2 spawnPosition = GlobalPosition + new Vector2(
			GD.RandRange(-10, 10),
			GD.RandRange(-10, 10)
		);
		DroppedResource.SpawnResource(spawnPosition, _selectedCraftingResource, 1);
	}

	public bool CanAffordRecipe(ResourceType recipeType, int amount)
	{
		if (!SmeltingRecipes.TryGetValue(recipeType, out var recipe)) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		foreach (var ingredient in recipe)
		{
			if (!resourcesManager.HasResource(ingredient.Key, ingredient.Value * amount))
			{
				return false;
			}
		}

		return true;
	}

	public bool ConsumeRecipeResources(ResourceType recipeType, int amount)
	{
		if (!SmeltingRecipes.TryGetValue(recipeType, out var recipe)) return false;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		foreach (var ingredient in recipe)
		{
			if (!resourcesManager.RemoveResource(ingredient.Key, ingredient.Value * amount))
			{
				return false;
			}
		}

		return true;
	}

	public bool CanBePlacedAt(Vector2I topLeftTile)
	{
		if (_customDataManager == null) return false;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = topLeftTile + new Vector2I(x, y);
				var tileData = _customDataManager.GetTileData(tilePos);

				if (tileData.ObjectTypePlaced != ObjectTypePlaced.None || tileData.Region <= 0)
				{
					return false;
				}
			}
		}

		return true;
	}

	public void SetPlacementFeedback(bool canPlace)
	{
		if (_sprite != null)
		{
			_sprite.Modulate = canPlace ? Colors.White : Colors.Red;
		}
	}

	public void SetSaveId(string saveId)
	{
		_saveId = saveId;
	}

	public string GetSaveId()
	{
		return _saveId;
	}

	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void LoadFromSave(Vector2I topLeftTilePosition, CustomDataLayerManager customDataManager, int health)
	{
		_topLeftTilePosition = topLeftTilePosition;
		_customDataManager = customDataManager;
		_currentHealth = health;

		// Mark tiles as occupied when loading from save
		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I tilePos = _topLeftTilePosition + new Vector2I(x, y);
				_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
			}
		}

		float centerX = (_topLeftTilePosition.X + 1.0f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 1.0f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);

		UpdateHPBar();

		_isPlaced = true;
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public ObjectType GetObjectType()
	{
		return BuildingType;
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		var distance = _topLeftTilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 2 && Math.Abs(distance.Y) <= 2;
	}

	public Vector2I GetTilePosition()
	{
		return _topLeftTilePosition;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	private void UpdateHPBar()
	{
		if (_healthBar == null) return;

		// Calculate health percentage
		float healthPercentage = (float)_currentHealth / MaxHealth;

		// If at full health, hide HP bar
		if (_currentHealth >= MaxHealth)
		{
			_healthBar.Hide();
		}
		else
		{
			// Show HP bar and set progress
			_healthBar.Show();
			_healthBar.SetProgress(healthPercentage);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (!_isPlaced) return;

		for (int x = 0; x < BUILDING_WIDTH; x++)
		{
			for (int y = 0; y < BUILDING_HEIGHT; y++)
			{
				Vector2I occupiedTile = _topLeftTilePosition + new Vector2I(x, y);
				if (occupiedTile == tilePosition)
				{
					Vector2I playerTile = GetPlayerTilePosition();
					if (CanBeHitFrom(playerTile))
					{
						TakeDamage(damage);
					}
					return;
				}
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}
}
